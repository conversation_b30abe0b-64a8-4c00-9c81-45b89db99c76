import { Socket } from 'net'
import { createLogger } from '../utils/logger.js'
import { createError } from '../middleware/errorHandler.js'

const logger = createLogger()

export interface StatusRequest {
  host: string
  port: number
  timeout?: number
}

export interface StatusResult {
  isOnline: boolean
  host: string
  port: number
  responseTime?: number
  timestamp: string
  error?: string
}

export class StatusService {
  private static readonly DEFAULT_TIMEOUT = 3000

  static async checkHostStatus(request: StatusRequest): Promise<StatusResult> {
    const { host, port, timeout = this.DEFAULT_TIMEOUT } = request
    const startTime = Date.now()

    logger.info('Checking host status', { host, port, timeout })

    try {
      const isOnline = await this.performTelnetCheck(host, port, timeout)
      const responseTime = Date.now() - startTime

      const result: StatusResult = {
        isOnline,
        host,
        port,
        responseTime,
        timestamp: new Date().toISOString(),
      }

      logger.info('Host status check completed', {
        ...result,
        duration: responseTime,
      })

      return result

    } catch (error) {
      const responseTime = Date.now() - startTime
      const errorMessage = error instanceof Error ? error.message : 'Unknown error'

      logger.warn('Host status check failed', {
        host,
        port,
        error: errorMessage,
        duration: responseTime,
      })

      return {
        isOnline: false,
        host,
        port,
        responseTime,
        timestamp: new Date().toISOString(),
        error: errorMessage,
      }
    }
  }

  private static async performTelnetCheck(
    host: string, 
    port: number, 
    timeout: number
  ): Promise<boolean> {
    return new Promise((resolve, reject) => {
      const socket = new Socket()
      let isResolved = false

      const cleanup = () => {
        if (!isResolved) {
          isResolved = true
          socket.destroy()
        }
      }

      const timeoutId = setTimeout(() => {
        cleanup()
        reject(new Error(`Connection timeout after ${timeout}ms`))
      }, timeout)

      socket.on('connect', () => {
        if (!isResolved) {
          isResolved = true
          clearTimeout(timeoutId)
          socket.destroy()
          resolve(true)
        }
      })

      socket.on('error', (error) => {
        if (!isResolved) {
          isResolved = true
          clearTimeout(timeoutId)
          socket.destroy()
          reject(error)
        }
      })

      socket.on('timeout', () => {
        if (!isResolved) {
          isResolved = true
          clearTimeout(timeoutId)
          socket.destroy()
          reject(new Error('Socket timeout'))
        }
      })

      try {
        socket.connect(port, host)
      } catch (error) {
        cleanup()
        reject(error)
      }
    })
  }

  static async checkMultipleHosts(
    requests: StatusRequest[]
  ): Promise<StatusResult[]> {
    logger.info('Checking multiple hosts', { count: requests.length })

    const promises = requests.map(request => 
      this.checkHostStatus(request).catch(error => ({
        isOnline: false,
        host: request.host,
        port: request.port,
        timestamp: new Date().toISOString(),
        error: error instanceof Error ? error.message : 'Unknown error',
      }))
    )

    const results = await Promise.all(promises)

    logger.info('Multiple host check completed', {
      total: results.length,
      online: results.filter(r => r.isOnline).length,
      offline: results.filter(r => !r.isOnline).length,
    })

    return results
  }

  static async waitForHostOnline(
    request: StatusRequest,
    maxWaitTime: number = 60000,
    checkInterval: number = 5000
  ): Promise<StatusResult> {
    const startTime = Date.now()
    const endTime = startTime + maxWaitTime

    logger.info('Waiting for host to come online', {
      ...request,
      maxWaitTime,
      checkInterval,
    })

    while (Date.now() < endTime) {
      const result = await this.checkHostStatus(request)
      
      if (result.isOnline) {
        logger.info('Host came online', {
          ...result,
          totalWaitTime: Date.now() - startTime,
        })
        return result
      }

      // Wait before next check
      await new Promise(resolve => setTimeout(resolve, checkInterval))
    }

    // Final check after timeout
    const finalResult = await this.checkHostStatus(request)
    
    logger.warn('Host did not come online within timeout', {
      ...finalResult,
      maxWaitTime,
      totalWaitTime: Date.now() - startTime,
    })

    return finalResult
  }
}

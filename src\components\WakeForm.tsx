import React, { useState } from 'react'
import { Power, Globe, Hash, Network } from 'lucide-react'

interface WakeFormProps {
  onWakeRequest: (host: string, macAddress: string, port: number, checkPort: number) => void
  isLoading: boolean
}

const WakeForm: React.FC<WakeFormProps> = ({ onWakeRequest, isLoading }) => {
  const [formData, setFormData] = useState({
    host: '',
    macAddress: '',
    port: 9,
    checkPort: 22,
  })

  const [errors, setErrors] = useState<Record<string, string>>({})

  const validateForm = () => {
    const newErrors: Record<string, string> = {}

    if (!formData.host.trim()) {
      newErrors.host = 'Host is required'
    }

    if (!formData.macAddress.trim()) {
      newErrors.macAddress = 'MAC address is required'
    } else if (!/^([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})$/.test(formData.macAddress)) {
      newErrors.macAddress = 'Invalid MAC address format (e.g., AA:BB:CC:DD:EE:FF)'
    }

    if (formData.port < 1 || formData.port > 65535) {
      newErrors.port = 'Port must be between 1 and 65535'
    }

    if (formData.checkPort < 1 || formData.checkPort > 65535) {
      newErrors.checkPort = 'Check port must be between 1 and 65535'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    
    if (validateForm()) {
      onWakeRequest(
        formData.host,
        formData.macAddress,
        formData.port,
        formData.checkPort
      )
    }
  }

  const handleInputChange = (field: string, value: string | number) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }))
    }
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* Host */}
      <div>
        <label htmlFor="host" className="block text-sm font-medium text-white mb-2">
          Remote Host
        </label>
        <div className="relative">
          <Globe className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
          <input
            id="host"
            type="text"
            value={formData.host}
            onChange={(e) => handleInputChange('host', e.target.value)}
            className={`w-full pl-10 pr-4 py-3 bg-white/10 border ${
              errors.host ? 'border-red-500' : 'border-white/20'
            } rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent`}
            placeholder="example.com or ***********"
            required
          />
        </div>
        {errors.host && <p className="mt-1 text-sm text-red-400">{errors.host}</p>}
      </div>

      {/* MAC Address */}
      <div>
        <label htmlFor="macAddress" className="block text-sm font-medium text-white mb-2">
          MAC Address
        </label>
        <div className="relative">
          <Network className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
          <input
            id="macAddress"
            type="text"
            value={formData.macAddress}
            onChange={(e) => handleInputChange('macAddress', e.target.value.toUpperCase())}
            className={`w-full pl-10 pr-4 py-3 bg-white/10 border ${
              errors.macAddress ? 'border-red-500' : 'border-white/20'
            } rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent`}
            placeholder="AA:BB:CC:DD:EE:FF"
            required
          />
        </div>
        {errors.macAddress && <p className="mt-1 text-sm text-red-400">{errors.macAddress}</p>}
      </div>

      {/* Ports */}
      <div className="grid grid-cols-2 gap-4">
        <div>
          <label htmlFor="port" className="block text-sm font-medium text-white mb-2">
            Wake Port
          </label>
          <div className="relative">
            <Hash className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
            <input
              id="port"
              type="number"
              min="1"
              max="65535"
              value={formData.port}
              onChange={(e) => handleInputChange('port', parseInt(e.target.value) || 9)}
              className={`w-full pl-10 pr-4 py-3 bg-white/10 border ${
                errors.port ? 'border-red-500' : 'border-white/20'
              } rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent`}
              required
            />
          </div>
          {errors.port && <p className="mt-1 text-sm text-red-400">{errors.port}</p>}
        </div>

        <div>
          <label htmlFor="checkPort" className="block text-sm font-medium text-white mb-2">
            Check Port
          </label>
          <div className="relative">
            <Hash className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
            <input
              id="checkPort"
              type="number"
              min="1"
              max="65535"
              value={formData.checkPort}
              onChange={(e) => handleInputChange('checkPort', parseInt(e.target.value) || 22)}
              className={`w-full pl-10 pr-4 py-3 bg-white/10 border ${
                errors.checkPort ? 'border-red-500' : 'border-white/20'
              } rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent`}
              required
            />
          </div>
          {errors.checkPort && <p className="mt-1 text-sm text-red-400">{errors.checkPort}</p>}
        </div>
      </div>

      {/* Submit Button */}
      <button
        type="submit"
        disabled={isLoading}
        className="w-full py-3 px-4 bg-green-600 hover:bg-green-700 disabled:bg-green-800 text-white font-medium rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 focus:ring-offset-transparent flex items-center justify-center space-x-2"
      >
        <Power className="h-5 w-5" />
        <span>{isLoading ? 'Sending Wake Signal...' : 'Wake Device'}</span>
      </button>

      {/* Help Text */}
      <div className="text-xs text-gray-400 space-y-1">
        <p>• <strong>Wake Port:</strong> Usually 9 (standard WoL port)</p>
        <p>• <strong>Check Port:</strong> Port to test connectivity (22=SSH, 80=HTTP, 3389=RDP)</p>
        <p>• <strong>MAC Address:</strong> Physical address of the target network card</p>
      </div>
    </form>
  )
}

export default WakeForm

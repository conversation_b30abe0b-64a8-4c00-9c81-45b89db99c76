import React from 'react'
import { <PERSON><PERSON>resh<PERSON><PERSON>, CheckCircle, XCircle, Clock, Loader } from 'lucide-react'

interface StatusDisplayProps {
  status: {
    isWaking: boolean
    isChecking: boolean
    lastWakeTime?: string
    targetHost?: string
    targetPort?: number
    isOnline?: boolean
  }
  onCheckStatus: () => void
}

const StatusDisplay: React.FC<StatusDisplayProps> = ({ status, onCheckStatus }) => {
  const formatTime = (isoString: string) => {
    return new Date(isoString).toLocaleString()
  }

  const getStatusColor = () => {
    if (status.isChecking) return 'text-yellow-400'
    if (status.isOnline === true) return 'text-green-400'
    if (status.isOnline === false) return 'text-red-400'
    return 'text-gray-400'
  }

  const getStatusIcon = () => {
    if (status.isChecking) return <Loader className="h-5 w-5 animate-spin" />
    if (status.isOnline === true) return <CheckCircle className="h-5 w-5" />
    if (status.isOnline === false) return <XCircle className="h-5 w-5" />
    return <Clock className="h-5 w-5" />
  }

  const getStatusText = () => {
    if (status.isWaking) return 'Sending wake signal...'
    if (status.isChecking) return 'Checking device status...'
    if (status.isOnline === true) return 'Device is online'
    if (status.isOnline === false) return 'Device is offline'
    return 'No status check performed'
  }

  return (
    <div className="space-y-6">
      {/* Current Status */}
      <div className="bg-white/5 border border-white/10 rounded-lg p-4">
        <div className="flex items-center justify-between mb-3">
          <h3 className="text-sm font-medium text-white">Current Status</h3>
          <button
            onClick={onCheckStatus}
            disabled={status.isChecking || !status.targetHost}
            className="p-2 text-gray-400 hover:text-white disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            title="Refresh status"
          >
            <RefreshCw className={`h-4 w-4 ${status.isChecking ? 'animate-spin' : ''}`} />
          </button>
        </div>
        
        <div className="flex items-center space-x-3">
          <div className={getStatusColor()}>
            {getStatusIcon()}
          </div>
          <div>
            <p className={`font-medium ${getStatusColor()}`}>
              {getStatusText()}
            </p>
            {status.targetHost && (
              <p className="text-sm text-gray-400">
                Target: {status.targetHost}:{status.targetPort}
              </p>
            )}
          </div>
        </div>
      </div>

      {/* Wake History */}
      {status.lastWakeTime && (
        <div className="bg-white/5 border border-white/10 rounded-lg p-4">
          <h3 className="text-sm font-medium text-white mb-3">Last Wake Signal</h3>
          <div className="flex items-center space-x-3">
            <Clock className="h-4 w-4 text-blue-400" />
            <div>
              <p className="text-sm text-white">
                {formatTime(status.lastWakeTime)}
              </p>
              <p className="text-xs text-gray-400">
                Magic packet sent successfully
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Status Legend */}
      <div className="bg-white/5 border border-white/10 rounded-lg p-4">
        <h3 className="text-sm font-medium text-white mb-3">Status Legend</h3>
        <div className="space-y-2 text-sm">
          <div className="flex items-center space-x-2">
            <CheckCircle className="h-4 w-4 text-green-400" />
            <span className="text-gray-300">Online - Device is responding</span>
          </div>
          <div className="flex items-center space-x-2">
            <XCircle className="h-4 w-4 text-red-400" />
            <span className="text-gray-300">Offline - Device is not responding</span>
          </div>
          <div className="flex items-center space-x-2">
            <Loader className="h-4 w-4 text-yellow-400" />
            <span className="text-gray-300">Checking - Testing connectivity</span>
          </div>
        </div>
      </div>

      {/* Tips */}
      <div className="bg-blue-500/10 border border-blue-500/20 rounded-lg p-4">
        <h3 className="text-sm font-medium text-blue-400 mb-2">💡 Tips</h3>
        <ul className="text-xs text-gray-300 space-y-1">
          <li>• It may take 30-60 seconds for a device to fully boot up</li>
          <li>• Ensure Wake-on-LAN is enabled in BIOS/UEFI settings</li>
          <li>• Network adapter must support WoL and be configured properly</li>
          <li>• Some routers may block WoL packets from external networks</li>
        </ul>
      </div>
    </div>
  )
}

export default StatusDisplay

# 演示设置指南

## 快速演示

### 1. 环境准备

确保您的系统已安装：
- Node.js 18 或更高版本
- npm 或 yarn

### 2. 启动应用

#### Windows 用户
```cmd
# 双击运行
start.bat

# 或者手动启动
npm install
cd backend && npm install && cd ..
npm run dev
# 在新终端窗口
cd backend && npm run dev
```

#### Linux/Mac 用户
```bash
# 给脚本执行权限
chmod +x start.sh
./start.sh

# 或者手动启动
npm install
cd backend && npm install && cd ..
npm run dev &
cd backend && npm run dev &
```

### 3. 访问应用

1. 打开浏览器访问: `http://localhost:3000`
2. 使用演示凭据登录:
   - 用户名: `admin`
   - 密码: `admin123`

### 4. 演示功能

#### 测试Wake-on-LAN功能

**示例配置 1 - 本地测试:**
- 远程主机: `localhost` 或 `127.0.0.1`
- MAC地址: `AA:BB:CC:DD:EE:FF` (示例MAC地址)
- 唤醒端口: `9`
- 检测端口: `22` (SSH) 或 `80` (HTTP)

**示例配置 2 - 局域网设备:**
- 远程主机: `*************` (替换为您的设备IP)
- MAC地址: 您设备的实际MAC地址
- 唤醒端口: `9`
- 检测端口: `22`, `80`, `443`, 或 `3389` (RDP)

#### 获取设备MAC地址

**Windows:**
```cmd
ipconfig /all
# 查找 "物理地址" 或 "Physical Address"
```

**Linux/Mac:**
```bash
ifconfig
# 查找 "ether" 或 "HWaddr"
```

### 5. 功能测试清单

- [ ] 用户登录/登出
- [ ] 输入设备信息
- [ ] 发送Wake-on-LAN包
- [ ] 检测设备状态
- [ ] 查看状态历史
- [ ] 响应式界面测试

### 6. 故障排除

#### 常见问题

**1. 无法发送Wake包**
- 检查MAC地址格式 (AA:BB:CC:DD:EE:FF)
- 确认网络连接
- 检查防火墙设置

**2. 状态检测失败**
- 确认目标端口开放
- 检查网络连通性
- 尝试不同的检测端口

**3. 前端无法连接后端**
- 确认后端服务运行在端口3001
- 检查CORS配置
- 查看浏览器控制台错误

#### 日志查看

**后端日志:**
```bash
cd backend
tail -f logs/combined.log
```

**浏览器控制台:**
- 按F12打开开发者工具
- 查看Console和Network标签

### 7. 高级配置

#### 修改认证凭据

编辑 `src/contexts/AuthContext.tsx`:
```typescript
// 第32行附近
if (username === 'your-username' && password === 'your-password') {
```

#### 修改服务器端口

**后端端口 (backend/.env):**
```env
PORT=3001
```

**前端代理 (vite.config.ts):**
```typescript
proxy: {
  '/api': {
    target: 'http://localhost:3001',
  },
},
```

#### 添加HTTPS支持

**后端 (backend/src/server.ts):**
```typescript
import https from 'https'
import fs from 'fs'

const options = {
  key: fs.readFileSync('path/to/private-key.pem'),
  cert: fs.readFileSync('path/to/certificate.pem')
}

https.createServer(options, app).listen(3001)
```

### 8. 生产部署

#### 构建应用
```bash
# 构建前端
npm run build

# 构建后端
cd backend
npm run build
```

#### 环境变量配置
```env
NODE_ENV=production
PORT=3001
CORS_ORIGIN=https://your-domain.com
LOG_LEVEL=warn
```

#### 使用PM2部署
```bash
npm install -g pm2

# 启动后端
cd backend
pm2 start dist/server.js --name "wake-backend"

# 启动前端 (使用nginx或其他静态服务器)
```

### 9. 安全注意事项

- 更改默认认证凭据
- 使用HTTPS连接
- 限制API访问IP
- 定期更新依赖包
- 配置防火墙规则

### 10. 支持与反馈

如遇到问题，请：
1. 检查本文档的故障排除部分
2. 查看GitHub Issues
3. 提交新的Issue报告

---

**祝您使用愉快！** 🚀

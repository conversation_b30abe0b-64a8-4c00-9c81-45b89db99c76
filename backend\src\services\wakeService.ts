import wol from 'wake_on_lan'
import { createLogger } from '../utils/logger.js'
import { createError } from '../middleware/errorHandler.js'

const logger = createLogger()

export interface WakeRequest {
  host: string
  macAddress: string
  port: number
  checkPort: number
}

export interface WakeResult {
  success: boolean
  message: string
  timestamp: string
}

export class WakeService {
  private static validateMacAddress(macAddress: string): boolean {
    const macRegex = /^([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})$/
    return macRegex.test(macAddress)
  }

  private static normalizeMacAddress(macAddress: string): string {
    // Remove any separators and convert to uppercase
    return macAddress.replace(/[:-]/g, '').toUpperCase()
  }

  static async sendWakePacket(request: WakeRequest): Promise<WakeResult> {
    try {
      const { host, macAddress, port } = request

      // Validate MAC address
      if (!this.validateMacAddress(macAddress)) {
        throw createError('Invalid MAC address format', 400)
      }

      // Normalize MAC address for wake_on_lan library
      const normalizedMac = this.normalizeMacAddress(macAddress)

      logger.info('Sending wake packet', {
        host,
        macAddress: normalizedMac,
        port,
      })

      // Send wake-on-lan packet
      await new Promise<void>((resolve, reject) => {
        wol.wake(normalizedMac, { address: host, port }, (error: Error | null) => {
          if (error) {
            reject(error)
          } else {
            resolve()
          }
        })
      })

      const result: WakeResult = {
        success: true,
        message: 'Wake packet sent successfully',
        timestamp: new Date().toISOString(),
      }

      logger.info('Wake packet sent successfully', {
        host,
        macAddress: normalizedMac,
        timestamp: result.timestamp,
      })

      return result

    } catch (error) {
      logger.error('Failed to send wake packet', {
        error: error instanceof Error ? error.message : 'Unknown error',
        request,
      })

      if (error instanceof Error && 'statusCode' in error) {
        throw error
      }

      throw createError('Failed to send wake packet: ' + (error instanceof Error ? error.message : 'Unknown error'))
    }
  }

  static async sendWakePacketWithRetry(
    request: WakeRequest, 
    maxRetries: number = 3
  ): Promise<WakeResult> {
    let lastError: Error | null = null

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        logger.info(`Wake attempt ${attempt}/${maxRetries}`, { request })
        return await this.sendWakePacket(request)
      } catch (error) {
        lastError = error instanceof Error ? error : new Error('Unknown error')
        logger.warn(`Wake attempt ${attempt} failed`, {
          error: lastError.message,
          attempt,
          maxRetries,
        })

        if (attempt < maxRetries) {
          // Wait before retry (exponential backoff)
          const delay = Math.pow(2, attempt - 1) * 1000
          await new Promise(resolve => setTimeout(resolve, delay))
        }
      }
    }

    throw lastError || createError('All wake attempts failed')
  }
}

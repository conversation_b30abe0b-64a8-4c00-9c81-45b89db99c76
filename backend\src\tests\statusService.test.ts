import { StatusService } from '../services/statusService.js'

describe('StatusService', () => {
  describe('checkHostStatus', () => {
    it('should return offline for invalid host', async () => {
      const request = {
        host: 'invalid-host-12345',
        port: 80,
        timeout: 1000,
      }

      const result = await StatusService.checkHostStatus(request)
      
      expect(result.isOnline).toBe(false)
      expect(result.host).toBe(request.host)
      expect(result.port).toBe(request.port)
      expect(result.error).toBeDefined()
    })

    it('should handle timeout correctly', async () => {
      const request = {
        host: '*************', // Likely non-existent IP
        port: 12345,
        timeout: 1000,
      }

      const startTime = Date.now()
      const result = await StatusService.checkHostStatus(request)
      const duration = Date.now() - startTime

      expect(result.isOnline).toBe(false)
      expect(duration).toBeGreaterThanOrEqual(1000)
      expect(duration).toBeLessThan(2000) // Should not take much longer than timeout
    })

    it('should return online for localhost on common ports', async () => {
      // This test might be environment-dependent
      const request = {
        host: 'localhost',
        port: 80, // Might not be available in all environments
        timeout: 3000,
      }

      const result = await StatusService.checkHostStatus(request)
      
      // We can't guarantee localhost:80 is available, so we just check the structure
      expect(result).toHaveProperty('isOnline')
      expect(result).toHaveProperty('host', 'localhost')
      expect(result).toHaveProperty('port', 80)
      expect(result).toHaveProperty('timestamp')
      expect(result).toHaveProperty('responseTime')
    })
  })

  describe('checkMultipleHosts', () => {
    it('should check multiple hosts', async () => {
      const requests = [
        { host: 'invalid-host-1', port: 80, timeout: 1000 },
        { host: 'invalid-host-2', port: 443, timeout: 1000 },
      ]

      const results = await StatusService.checkMultipleHosts(requests)

      expect(results).toHaveLength(2)
      expect(results[0].isOnline).toBe(false)
      expect(results[1].isOnline).toBe(false)
    })
  })

  describe('waitForHostOnline', () => {
    it('should timeout when host never comes online', async () => {
      const request = {
        host: 'invalid-host-12345',
        port: 80,
        timeout: 1000,
      }

      const startTime = Date.now()
      const result = await StatusService.waitForHostOnline(request, 3000, 1000)
      const duration = Date.now() - startTime

      expect(result.isOnline).toBe(false)
      expect(duration).toBeGreaterThanOrEqual(3000)
    })
  })
})

# Remote Wake-on-LAN

一个基于液态玻璃效果的远程Wake-on-LAN管理系统，支持通过网络远程唤醒设备并检测设备状态。

## 功能特性

- 🌟 **液态玻璃UI效果** - 基于 liquid-glass-react 的现代化界面
- 🔐 **安全认证** - 内置用户认证系统，保护敏感操作
- 🌐 **远程唤醒** - 支持通过域名/IP远程发送Wake-on-LAN魔术包
- 📡 **状态检测** - 使用Telnet检测目标设备是否在线
- ⚡ **实时反馈** - 实时显示唤醒状态和设备连接状态
- 🔄 **重试机制** - 支持自动重试和状态轮询

## 技术栈

### 前端
- React 18 + TypeScript
- Vite (构建工具)
- Tailwind CSS (样式)
- liquid-glass-react (液态玻璃效果)
- React Router (路由)
- Axios (HTTP客户端)
- Lucide React (图标)

### 后端
- Node.js + Express
- TypeScript
- wake_on_lan (WoL包发送)
- Winston (日志)
- Helmet (安全)
- CORS (跨域)

## 快速开始

### 环境要求

- Node.js 18+ 
- npm 或 yarn

### 安装依赖

```bash
# 安装前端依赖
npm install

# 安装后端依赖
cd backend
npm install
```

### 启动开发服务器

```bash
# 启动后端服务 (端口 3001)
cd backend
npm run dev

# 启动前端服务 (端口 3000)
npm run dev
```

### 访问应用

打开浏览器访问 `http://localhost:3000`

**默认登录凭据:**
- 用户名: `admin`
- 密码: `admin123`

## 使用说明

### 1. 登录系统
使用默认凭据登录系统，或配置自己的认证方式。

### 2. 配置目标设备
- **远程主机**: 目标设备的域名或IP地址
- **MAC地址**: 目标设备网卡的物理地址 (格式: AA:BB:CC:DD:EE:FF)
- **唤醒端口**: 通常为9 (标准WoL端口)
- **检测端口**: 用于状态检测的端口 (如22=SSH, 80=HTTP, 3389=RDP)

### 3. 发送唤醒信号
点击"Wake Device"按钮发送魔术包，系统会自动检测设备状态。

### 4. 监控设备状态
右侧面板会实时显示设备的在线状态和连接信息。

## API 接口

### Wake-on-LAN 接口

#### 发送唤醒包
```http
POST /api/wake
Content-Type: application/json

{
  "host": "example.com",
  "macAddress": "AA:BB:CC:DD:EE:FF",
  "port": 9,
  "checkPort": 22
}
```

#### 带重试的唤醒包
```http
POST /api/wake-with-retry
Content-Type: application/json

{
  "host": "example.com",
  "macAddress": "AA:BB:CC:DD:EE:FF",
  "port": 9,
  "checkPort": 22,
  "maxRetries": 3
}
```

### 状态检测接口

#### 检测单个主机
```http
POST /api/check-status
Content-Type: application/json

{
  "host": "example.com",
  "port": 22,
  "timeout": 3000
}
```

#### 等待主机上线
```http
POST /api/wait-for-online
Content-Type: application/json

{
  "host": "example.com",
  "port": 22,
  "maxWaitTime": 60000,
  "checkInterval": 5000
}
```

## 配置说明

### 环境变量

后端配置文件 `backend/.env`:

```env
# 服务器配置
PORT=3001
NODE_ENV=development

# CORS配置
CORS_ORIGIN=http://localhost:3000

# 日志级别
LOG_LEVEL=info

# Wake-on-LAN配置
WOL_TIMEOUT=5000
TELNET_TIMEOUT=3000
```

### Wake-on-LAN 设置要求

为了使远程唤醒功能正常工作，目标设备需要满足以下条件:

1. **BIOS/UEFI设置**
   - 启用 Wake-on-LAN 功能
   - 启用 PCI 设备唤醒
   - 保持网络适配器供电

2. **网络适配器设置**
   - 启用"允许此设备唤醒计算机"
   - 启用"只允许魔术包唤醒计算机"

3. **网络环境**
   - 确保路由器支持并转发WoL包
   - 配置端口转发 (如果通过互联网访问)
   - 目标设备必须连接到网络 (有线连接推荐)

## 部署

### 生产环境构建

```bash
# 构建前端
npm run build

# 构建后端
cd backend
npm run build
```

### Docker 部署

```dockerfile
# Dockerfile 示例
FROM node:18-alpine

WORKDIR /app

# 复制并安装依赖
COPY package*.json ./
RUN npm ci --only=production

# 复制构建文件
COPY dist ./dist

EXPOSE 3001

CMD ["npm", "start"]
```

## 故障排除

### 常见问题

1. **设备无法唤醒**
   - 检查MAC地址格式是否正确
   - 确认目标设备支持并启用了WoL
   - 检查网络连接和路由器配置

2. **状态检测失败**
   - 确认检测端口是否开放
   - 检查防火墙设置
   - 验证网络连通性

3. **认证问题**
   - 确认使用正确的登录凭据
   - 检查浏览器本地存储

## 许可证

MIT License

## 贡献

欢迎提交 Issue 和 Pull Request！

## 支持

如有问题，请创建 GitHub Issue 或联系开发团队。

#!/bin/bash

echo "Starting Remote Wake-on-LAN Application..."
echo

echo "Installing dependencies..."
npm install
cd backend
npm install
cd ..

echo
echo "Starting backend server..."
cd backend
npm run dev &
BACKEND_PID=$!
cd ..

sleep 3

echo "Starting frontend server..."
npm run dev &
FRONTEND_PID=$!

echo
echo "Both servers are starting..."
echo "Backend: http://localhost:3001"
echo "Frontend: http://localhost:3000"
echo
echo "Press Ctrl+C to stop both servers..."

# Function to cleanup processes
cleanup() {
    echo
    echo "Stopping servers..."
    kill $BACKEND_PID 2>/dev/null
    kill $FRONTEND_PID 2>/dev/null
    exit 0
}

# Trap Ctrl+C
trap cleanup INT

# Wait for processes
wait

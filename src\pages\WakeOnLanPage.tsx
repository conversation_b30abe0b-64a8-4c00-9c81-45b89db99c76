import React, { useState } from 'react'
import LiquidGlass from 'liquid-glass-react'
import { useAuth } from '../contexts/AuthContext'
import { Power, Wifi, LogOut, Monitor, Network, Clock } from 'lucide-react'
import WakeForm from '../components/WakeForm'
import StatusDisplay from '../components/StatusDisplay'

const WakeOnLanPage: React.FC = () => {
  const { user, logout } = useAuth()
  const [wakeStatus, setWakeStatus] = useState<{
    isWaking: boolean
    isChecking: boolean
    lastWakeTime?: string
    targetHost?: string
    targetPort?: number
    isOnline?: boolean
  }>({
    isWaking: false,
    isChecking: false,
  })

  const handleWakeRequest = async (host: string, macAddress: string, port: number, checkPort: number) => {
    setWakeStatus(prev => ({ ...prev, isWaking: true, targetHost: host, targetPort: checkPort }))
    
    try {
      // Call wake API
      const response = await fetch('/api/wake', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          host,
          macAddress,
          port,
          checkPort,
        }),
      })

      const result = await response.json()
      
      if (result.success) {
        setWakeStatus(prev => ({
          ...prev,
          isWaking: false,
          lastWakeTime: new Date().toISOString(),
        }))
        
        // Start checking status
        checkHostStatus(host, checkPort)
      } else {
        throw new Error(result.error || 'Wake request failed')
      }
    } catch (error) {
      console.error('Wake error:', error)
      setWakeStatus(prev => ({ ...prev, isWaking: false }))
      alert('Failed to send wake signal: ' + (error as Error).message)
    }
  }

  const checkHostStatus = async (host: string, port: number) => {
    setWakeStatus(prev => ({ ...prev, isChecking: true }))
    
    try {
      const response = await fetch('/api/check-status', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ host, port }),
      })

      const result = await response.json()
      
      setWakeStatus(prev => ({
        ...prev,
        isChecking: false,
        isOnline: result.isOnline,
      }))
    } catch (error) {
      console.error('Status check error:', error)
      setWakeStatus(prev => ({ ...prev, isChecking: false, isOnline: false }))
    }
  }

  return (
    <div className="min-h-screen p-4">
      {/* Header */}
      <div className="max-w-6xl mx-auto mb-8">
        <div className="flex justify-between items-center">
          <div className="flex items-center space-x-3">
            <Wifi className="h-8 w-8 text-blue-400" />
            <div>
              <h1 className="text-2xl font-bold text-white">Remote Wake-on-LAN</h1>
              <p className="text-gray-300">Welcome, {user?.username}</p>
            </div>
          </div>
          
          <LiquidGlass
            displacementScale={40}
            blurAmount={0.08}
            saturation={110}
            aberrationIntensity={1}
            elasticity={0.15}
            cornerRadius={8}
            padding="8px 16px"
            onClick={logout}
            className="cursor-pointer"
          >
            <div className="flex items-center space-x-2 text-white">
              <LogOut className="h-4 w-4" />
              <span>Logout</span>
            </div>
          </LiquidGlass>
        </div>
      </div>

      <div className="max-w-6xl mx-auto grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Wake Form */}
        <div>
          <LiquidGlass
            displacementScale={60}
            blurAmount={0.1}
            saturation={130}
            aberrationIntensity={1.5}
            elasticity={0.25}
            cornerRadius={16}
            className="p-6"
          >
            <div className="flex items-center space-x-3 mb-6">
              <Power className="h-6 w-6 text-green-400" />
              <h2 className="text-xl font-semibold text-white">Wake Remote Device</h2>
            </div>
            
            <WakeForm 
              onWakeRequest={handleWakeRequest}
              isLoading={wakeStatus.isWaking}
            />
          </LiquidGlass>
        </div>

        {/* Status Display */}
        <div>
          <LiquidGlass
            displacementScale={60}
            blurAmount={0.1}
            saturation={130}
            aberrationIntensity={1.5}
            elasticity={0.25}
            cornerRadius={16}
            className="p-6"
          >
            <div className="flex items-center space-x-3 mb-6">
              <Monitor className="h-6 w-6 text-blue-400" />
              <h2 className="text-xl font-semibold text-white">Device Status</h2>
            </div>
            
            <StatusDisplay 
              status={wakeStatus}
              onCheckStatus={() => {
                if (wakeStatus.targetHost && wakeStatus.targetPort) {
                  checkHostStatus(wakeStatus.targetHost, wakeStatus.targetPort)
                }
              }}
            />
          </LiquidGlass>
        </div>
      </div>

      {/* Info Section */}
      <div className="max-w-6xl mx-auto mt-8">
        <LiquidGlass
          displacementScale={50}
          blurAmount={0.08}
          saturation={120}
          aberrationIntensity={1}
          elasticity={0.2}
          cornerRadius={12}
          className="p-6"
        >
          <div className="flex items-center space-x-3 mb-4">
            <Network className="h-5 w-5 text-purple-400" />
            <h3 className="text-lg font-medium text-white">How it works</h3>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-gray-300">
            <div className="flex items-start space-x-2">
              <div className="w-2 h-2 bg-blue-400 rounded-full mt-2 flex-shrink-0"></div>
              <div>
                <p className="font-medium text-white">Magic Packet</p>
                <p>Sends a Wake-on-LAN magic packet to the target device's MAC address</p>
              </div>
            </div>
            <div className="flex items-start space-x-2">
              <div className="w-2 h-2 bg-green-400 rounded-full mt-2 flex-shrink-0"></div>
              <div>
                <p className="font-medium text-white">Remote Wake</p>
                <p>Wakes up devices over the internet through specified host and port</p>
              </div>
            </div>
            <div className="flex items-start space-x-2">
              <div className="w-2 h-2 bg-purple-400 rounded-full mt-2 flex-shrink-0"></div>
              <div>
                <p className="font-medium text-white">Status Check</p>
                <p>Uses Telnet to verify if the device is online and responsive</p>
              </div>
            </div>
          </div>
        </LiquidGlass>
      </div>
    </div>
  )
}

export default WakeOnLanPage

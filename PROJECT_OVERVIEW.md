# 项目概览 - Remote Wake-on-LAN

## 项目结构

```
remote wake/
├── frontend/                    # React前端应用
│   ├── src/
│   │   ├── components/         # 可复用组件
│   │   │   ├── ProtectedRoute.tsx
│   │   │   ├── WakeForm.tsx
│   │   │   └── StatusDisplay.tsx
│   │   ├── contexts/           # React上下文
│   │   │   └── AuthContext.tsx
│   │   ├── pages/              # 页面组件
│   │   │   ├── LoginPage.tsx
│   │   │   └── WakeOnLanPage.tsx
│   │   ├── App.tsx
│   │   ├── main.tsx
│   │   └── index.css
│   ├── package.json
│   ├── vite.config.ts
│   ├── tailwind.config.js
│   └── tsconfig.json
├── backend/                     # Node.js后端API
│   ├── src/
│   │   ├── services/           # 业务逻辑服务
│   │   │   ├── wakeService.ts
│   │   │   └── statusService.ts
│   │   ├── routes/             # API路由
│   │   │   ├── wake.ts
│   │   │   └── status.ts
│   │   ├── middleware/         # 中间件
│   │   │   └── errorHandler.ts
│   │   ├── utils/              # 工具函数
│   │   │   └── logger.ts
│   │   ├── tests/              # 测试文件
│   │   └── server.ts
│   ├── package.json
│   ├── tsconfig.json
│   ├── jest.config.js
│   └── .env
├── README.md                    # 项目说明
├── DEMO_SETUP.md               # 演示设置指南
├── PROJECT_OVERVIEW.md         # 项目概览 (本文件)
├── start.bat                   # Windows启动脚本
└── start.sh                    # Linux/Mac启动脚本
```

## 核心功能模块

### 1. 认证系统 (Authentication)
- **文件**: `src/contexts/AuthContext.tsx`, `src/pages/LoginPage.tsx`
- **功能**: 用户登录验证，会话管理
- **特性**: 
  - 简单的用户名/密码认证
  - 本地存储会话状态
  - 路由保护

### 2. Wake-on-LAN服务 (Wake Service)
- **文件**: `backend/src/services/wakeService.ts`
- **功能**: 发送魔术包唤醒远程设备
- **特性**:
  - MAC地址验证和格式化
  - 重试机制
  - 错误处理和日志记录

### 3. 状态检测服务 (Status Service)
- **文件**: `backend/src/services/statusService.ts`
- **功能**: 使用Telnet检测设备在线状态
- **特性**:
  - TCP连接测试
  - 超时控制
  - 批量检测
  - 轮询等待

### 4. 用户界面 (UI Components)
- **文件**: `src/components/`, `src/pages/`
- **功能**: 液态玻璃效果的现代化界面
- **特性**:
  - 响应式设计
  - 实时状态反馈
  - 表单验证
  - 错误提示

## 技术架构

### 前端架构
```
Browser
  ↓
React App (Vite)
  ↓
Components (Liquid Glass UI)
  ↓
API Client (Axios)
  ↓
Backend API
```

### 后端架构
```
Express Server
  ↓
Routes (wake.ts, status.ts)
  ↓
Services (wakeService, statusService)
  ↓
External Libraries (wake_on_lan, net)
  ↓
Network (UDP/TCP)
```

## API设计

### Wake-on-LAN API
- `POST /api/wake` - 发送单次唤醒包
- `POST /api/wake-with-retry` - 带重试的唤醒包
- `GET /api/wake/history` - 唤醒历史 (预留)

### 状态检测API
- `POST /api/check-status` - 检测单个主机状态
- `POST /api/check-multiple-status` - 批量状态检测
- `POST /api/wait-for-online` - 等待主机上线
- `GET /api/status/health` - 服务健康检查

## 数据流

### 唤醒流程
1. 用户在前端输入设备信息
2. 前端验证表单数据
3. 发送POST请求到 `/api/wake`
4. 后端验证MAC地址格式
5. 使用wake_on_lan库发送UDP包
6. 返回操作结果
7. 前端显示状态并开始检测

### 状态检测流程
1. 前端发送POST请求到 `/api/check-status`
2. 后端创建TCP Socket连接
3. 尝试连接指定主机和端口
4. 根据连接结果返回在线状态
5. 前端更新UI显示状态

## 安全考虑

### 认证安全
- 简单的用户名/密码验证 (演示用)
- 会话状态存储在localStorage
- 路由级别的访问控制

### 网络安全
- CORS配置限制跨域访问
- Helmet中间件提供基础安全头
- 输入验证防止注入攻击

### 建议改进
- 实现JWT令牌认证
- 添加HTTPS支持
- 实现API密钥验证
- 添加请求频率限制

## 性能优化

### 前端优化
- Vite构建工具提供快速开发体验
- 组件懒加载
- 液态玻璃效果的性能优化

### 后端优化
- 异步处理所有网络操作
- 连接超时控制
- 错误处理和重试机制
- 日志记录用于监控

## 扩展性

### 功能扩展
- 设备管理 (保存常用设备)
- 批量唤醒
- 定时唤醒
- 唤醒历史记录
- 设备分组管理

### 技术扩展
- 数据库集成 (MongoDB/PostgreSQL)
- 用户管理系统
- WebSocket实时通信
- 移动端应用
- Docker容器化

## 部署选项

### 开发环境
- 前端: Vite开发服务器 (端口3000)
- 后端: tsx watch模式 (端口3001)

### 生产环境
- 前端: 静态文件部署 (Nginx/Apache)
- 后端: Node.js服务器 (PM2/Docker)
- 反向代理: Nginx
- HTTPS: Let's Encrypt

## 监控和日志

### 日志系统
- Winston日志库
- 分级日志 (error, warn, info, debug)
- 文件和控制台输出
- 结构化日志格式

### 监控指标
- API响应时间
- 错误率统计
- 唤醒成功率
- 设备在线状态

## 测试策略

### 单元测试
- Jest测试框架
- 服务层测试覆盖
- 模拟网络调用

### 集成测试
- API端点测试
- 前后端集成测试

### 手动测试
- 用户界面测试
- 跨浏览器兼容性
- 网络环境测试

---

这个项目展示了现代Web应用的完整架构，结合了美观的UI设计和实用的网络管理功能。

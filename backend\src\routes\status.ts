import { Router, Request, Response, NextFunction } from 'express'
import { StatusService, StatusRequest } from '../services/statusService.js'
import { createLogger } from '../utils/logger.js'
import { createError } from '../middleware/errorHandler.js'

const router = Router()
const logger = createLogger()

// Validation middleware for status requests
const validateStatusRequest = (req: Request, res: Response, next: NextFunction) => {
  const { host, port } = req.body

  if (!host || typeof host !== 'string') {
    return next(createError('Host is required and must be a string', 400))
  }

  if (!port || typeof port !== 'number' || port < 1 || port > 65535) {
    return next(createError('Port is required and must be a number between 1 and 65535', 400))
  }

  next()
}

// POST /api/check-status - Check if a host is online
router.post('/check-status', validateStatusRequest, async (req: Request, res: Response, next: NextFunction) => {
  try {
    const statusRequest: StatusRequest = {
      host: req.body.host,
      port: req.body.port,
      timeout: req.body.timeout || 3000,
    }

    logger.info('Received status check request', {
      ...statusRequest,
      ip: req.ip,
    })

    const result = await StatusService.checkHostStatus(statusRequest)

    res.json({
      success: true,
      data: result,
      message: 'Status check completed',
    })

  } catch (error) {
    next(error)
  }
})

// POST /api/check-multiple-status - Check multiple hosts
router.post('/check-multiple-status', async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { hosts } = req.body

    if (!Array.isArray(hosts) || hosts.length === 0) {
      return next(createError('Hosts array is required and must not be empty', 400))
    }

    if (hosts.length > 10) {
      return next(createError('Maximum 10 hosts allowed per request', 400))
    }

    // Validate each host
    for (const host of hosts) {
      if (!host.host || typeof host.host !== 'string') {
        return next(createError('Each host must have a valid host string', 400))
      }
      if (!host.port || typeof host.port !== 'number' || host.port < 1 || host.port > 65535) {
        return next(createError('Each host must have a valid port number', 400))
      }
    }

    const statusRequests: StatusRequest[] = hosts.map(host => ({
      host: host.host,
      port: host.port,
      timeout: host.timeout || 3000,
    }))

    logger.info('Received multiple status check request', {
      hostCount: statusRequests.length,
      hosts: statusRequests.map(r => `${r.host}:${r.port}`),
      ip: req.ip,
    })

    const results = await StatusService.checkMultipleHosts(statusRequests)

    res.json({
      success: true,
      data: results,
      message: 'Multiple status check completed',
    })

  } catch (error) {
    next(error)
  }
})

// POST /api/wait-for-online - Wait for a host to come online
router.post('/wait-for-online', validateStatusRequest, async (req: Request, res: Response, next: NextFunction) => {
  try {
    const statusRequest: StatusRequest = {
      host: req.body.host,
      port: req.body.port,
      timeout: req.body.timeout || 3000,
    }

    const maxWaitTime = req.body.maxWaitTime || 60000 // 1 minute default
    const checkInterval = req.body.checkInterval || 5000 // 5 seconds default

    if (typeof maxWaitTime !== 'number' || maxWaitTime < 1000 || maxWaitTime > 300000) {
      return next(createError('Max wait time must be between 1000ms and 300000ms (5 minutes)', 400))
    }

    if (typeof checkInterval !== 'number' || checkInterval < 1000 || checkInterval > 30000) {
      return next(createError('Check interval must be between 1000ms and 30000ms', 400))
    }

    logger.info('Received wait for online request', {
      ...statusRequest,
      maxWaitTime,
      checkInterval,
      ip: req.ip,
    })

    const result = await StatusService.waitForHostOnline(statusRequest, maxWaitTime, checkInterval)

    res.json({
      success: true,
      data: result,
      message: result.isOnline ? 'Host is now online' : 'Host did not come online within timeout',
    })

  } catch (error) {
    next(error)
  }
})

// GET /api/status/health - Health check for the status service
router.get('/status/health', (req: Request, res: Response) => {
  res.json({
    success: true,
    data: {
      service: 'status-service',
      status: 'healthy',
      timestamp: new Date().toISOString(),
    },
    message: 'Status service is healthy',
  })
})

export { router as statusRoutes }

{"name": "remote-wake-backend", "version": "1.0.0", "description": "Backend API for Remote Wake-on-LAN application", "main": "dist/server.js", "type": "module", "scripts": {"dev": "tsx watch src/server.ts", "build": "tsc", "start": "node dist/server.js", "test": "jest"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "dotenv": "^16.3.1", "wake_on_lan": "^1.0.0", "net": "^1.0.2", "winston": "^3.11.0"}, "devDependencies": {"@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/node": "^20.9.0", "tsx": "^4.6.0", "typescript": "^5.2.2", "jest": "^29.7.0", "@types/jest": "^29.5.8"}, "keywords": ["wake-on-lan", "remote-wake", "network", "api"], "author": "Remote Wake Team", "license": "MIT"}
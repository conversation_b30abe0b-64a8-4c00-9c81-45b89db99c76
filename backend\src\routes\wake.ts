import { Router, Request, Response, NextFunction } from 'express'
import { WakeService, WakeRequest } from '../services/wakeService.js'
import { createLogger } from '../utils/logger.js'
import { createError } from '../middleware/errorHandler.js'

const router = Router()
const logger = createLogger()

// Validation middleware for wake requests
const validateWakeRequest = (req: Request, res: Response, next: NextFunction) => {
  const { host, macAddress, port, checkPort } = req.body

  if (!host || typeof host !== 'string') {
    return next(createError('Host is required and must be a string', 400))
  }

  if (!macAddress || typeof macAddress !== 'string') {
    return next(createError('MAC address is required and must be a string', 400))
  }

  if (port !== undefined && (typeof port !== 'number' || port < 1 || port > 65535)) {
    return next(createError('Port must be a number between 1 and 65535', 400))
  }

  if (checkPort !== undefined && (typeof checkPort !== 'number' || checkPort < 1 || checkPort > 65535)) {
    return next(createError('Check port must be a number between 1 and 65535', 400))
  }

  next()
}

// POST /api/wake - Send wake-on-lan packet
router.post('/wake', validateWakeRequest, async (req: Request, res: Response, next: NextFunction) => {
  try {
    const wakeRequest: WakeRequest = {
      host: req.body.host,
      macAddress: req.body.macAddress,
      port: req.body.port || 9, // Default WoL port
      checkPort: req.body.checkPort || 22, // Default SSH port
    }

    logger.info('Received wake request', {
      host: wakeRequest.host,
      macAddress: wakeRequest.macAddress,
      port: wakeRequest.port,
      checkPort: wakeRequest.checkPort,
      ip: req.ip,
    })

    const result = await WakeService.sendWakePacket(wakeRequest)

    res.json({
      success: true,
      data: result,
      message: 'Wake packet sent successfully',
    })

  } catch (error) {
    next(error)
  }
})

// POST /api/wake-with-retry - Send wake-on-lan packet with retry logic
router.post('/wake-with-retry', validateWakeRequest, async (req: Request, res: Response, next: NextFunction) => {
  try {
    const wakeRequest: WakeRequest = {
      host: req.body.host,
      macAddress: req.body.macAddress,
      port: req.body.port || 9,
      checkPort: req.body.checkPort || 22,
    }

    const maxRetries = req.body.maxRetries || 3

    if (typeof maxRetries !== 'number' || maxRetries < 1 || maxRetries > 10) {
      return next(createError('Max retries must be a number between 1 and 10', 400))
    }

    logger.info('Received wake request with retry', {
      ...wakeRequest,
      maxRetries,
      ip: req.ip,
    })

    const result = await WakeService.sendWakePacketWithRetry(wakeRequest, maxRetries)

    res.json({
      success: true,
      data: result,
      message: 'Wake packet sent successfully with retry logic',
    })

  } catch (error) {
    next(error)
  }
})

// GET /api/wake/history - Get wake history (placeholder for future implementation)
router.get('/wake/history', (req: Request, res: Response) => {
  // This could be implemented with a database to store wake history
  res.json({
    success: true,
    data: [],
    message: 'Wake history feature not yet implemented',
  })
})

export { router as wakeRoutes }
